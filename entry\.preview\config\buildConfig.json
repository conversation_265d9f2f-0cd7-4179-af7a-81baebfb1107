{"deviceType": "phone,tablet,2in1", "buildMode": "debug", "note": "false", "logLevel": "3", "isPreview": "true", "port": "29900", "checkEntry": "true", "localPropertiesPath": "D:\\Code\\harmony\\harmony_shop\\local.properties", "Path": "C:\\Program Files\\Huawei\\DevEco Studio\\tools\\node\\", "aceProfilePath": "D:\\Code\\harmony\\harmony_shop\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile", "hapMode": "false", "img2bin": "true", "projectProfilePath": "D:\\Code\\harmony\\harmony_shop\\build-profile.json5", "watchMode": "true", "appResource": "D:\\Code\\harmony\\harmony_shop\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt", "aceBuildJson": "D:\\Code\\harmony\\harmony_shop\\entry\\.preview\\default\\intermediates\\loader\\default\\loader.json", "aceModuleRoot": "D:\\Code\\harmony\\harmony_shop\\entry\\src\\main\\ets", "aceSoPath": "D:\\Code\\harmony\\harmony_shop\\entry\\.preview\\cache\\nativeDependencies.txt", "cachePath": "D:\\Code\\harmony\\harmony_shop\\entry\\.preview\\cache\\.default", "aceModuleBuild": "D:\\Code\\harmony\\harmony_shop\\entry\\.preview\\default\\intermediates\\assets\\default\\ets", "aceModuleJsonPath": "D:\\Code\\harmony\\harmony_shop\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", "stageRouterConfig": {"paths": ["D:\\Code\\harmony\\harmony_shop\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", "D:\\Code\\harmony\\harmony_shop\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json"], "contents": ["{\"module\":{\"pages\":\"$profile:main_pages\",\"name\":\"entry\"}}", "{\"src\":[\"pages/Index\",\"pages/LoginPage\",\"pages/RegisterPage\",\"pages/CartPage\",\"pages/DetailsPage\",\"pages/CheckoutSuccessPage\",\"view/BottomTabs\"]}"]}}