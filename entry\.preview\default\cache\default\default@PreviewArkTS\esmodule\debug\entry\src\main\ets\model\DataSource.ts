import type { Product } from './Product';
/**
 * 硬编码的商品数据源 (静态数据)
 */
export class DataSource {
    /**
     * 获取所有商品数据
     */
    static getAllProducts(): Product[] {
        return [
            {
                id: 1,
                name: '商品1',
                price: 99.99,
                image: 'product1.jpg',
                description: '这是商品1的描述'
            },
            {
                id: 2,
                name: '商品2',
                price: 199.99,
                image: 'product2.jpg',
                description: '这是商品2的描述'
            },
            {
                id: 3,
                name: '商品3',
                price: 299.99,
                image: 'product3.jpg',
                description: '这是商品3的描述'
            }
        ];
    }
    /**
     * 根据ID获取商品
     */
    static getProductById(id: number): Product | undefined {
        return this.getAllProducts().find(product => product.id === id);
    }
}
