if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface DetailsPage_Params {
    product?: Product | undefined;
    quantity?: number;
    cartViewModel?: CartViewModel;
}
import type { Product } from '../model/Product';
import { DataSource } from "@normalized:N&&&entry/src/main/ets/model/DataSource&";
import { CartViewModel } from "@normalized:N&&&entry/src/main/ets/viewmodel/CartViewModel&";
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
class DetailsPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__product = new ObservedPropertyObjectPU(undefined, this, "product");
        this.__quantity = new ObservedPropertySimplePU(1, this, "quantity");
        this.cartViewModel = new CartViewModel();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: DetailsPage_Params) {
        if (params.product !== undefined) {
            this.product = params.product;
        }
        if (params.quantity !== undefined) {
            this.quantity = params.quantity;
        }
        if (params.cartViewModel !== undefined) {
            this.cartViewModel = params.cartViewModel;
        }
    }
    updateStateVars(params: DetailsPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__product.purgeDependencyOnElmtId(rmElmtId);
        this.__quantity.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__product.aboutToBeDeleted();
        this.__quantity.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __product: ObservedPropertyObjectPU<Product | undefined>;
    get product() {
        return this.__product.get();
    }
    set product(newValue: Product | undefined) {
        this.__product.set(newValue);
    }
    private __quantity: ObservedPropertySimplePU<number>;
    get quantity() {
        return this.__quantity.get();
    }
    set quantity(newValue: number) {
        this.__quantity.set(newValue);
    }
    private cartViewModel: CartViewModel;
    aboutToAppear() {
        // 获取路由参数
        const params = router.getParams() as Record<string, Object>;
        if (params && params.productId) {
            this.product = DataSource.getProductById(params.productId as number);
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/DetailsPage.ets(26:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#f5f5f5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/DetailsPage.ets(28:7)", "entry");
            // 顶部导航栏
            Row.width('100%');
            // 顶部导航栏
            Row.height(56);
            // 顶部导航栏
            Row.padding({ left: 16, right: 16 });
            // 顶部导航栏
            Row.backgroundColor('#fff');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('返回');
            Button.debugLine("entry/src/main/ets/pages/DetailsPage.ets(29:9)", "entry");
            Button.onClick(() => {
                router.back();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/DetailsPage.ets(34:9)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('商品详情');
            Text.debugLine("entry/src/main/ets/pages/DetailsPage.ets(36:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Medium);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/DetailsPage.ets(40:9)", "entry");
        }, Blank);
        Blank.pop();
        // 顶部导航栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.product) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Scroll.create();
                        Scroll.debugLine("entry/src/main/ets/pages/DetailsPage.ets(48:9)", "entry");
                        Scroll.layoutWeight(1);
                    }, Scroll);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/DetailsPage.ets(49:11)", "entry");
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 商品图片
                        Text.create('商品图片');
                        Text.debugLine("entry/src/main/ets/pages/DetailsPage.ets(51:13)", "entry");
                        // 商品图片
                        Text.width('100%');
                        // 商品图片
                        Text.height(300);
                        // 商品图片
                        Text.backgroundColor('#f0f0f0');
                        // 商品图片
                        Text.textAlign(TextAlign.Center);
                        // 商品图片
                        Text.fontSize(16);
                        // 商品图片
                        Text.fontColor('#999');
                    }, Text);
                    // 商品图片
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 商品信息
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/DetailsPage.ets(60:13)", "entry");
                        // 商品信息
                        Column.width('100%');
                        // 商品信息
                        Column.padding(16);
                        // 商品信息
                        Column.alignItems(HorizontalAlign.Start);
                        // 商品信息
                        Column.backgroundColor('#fff');
                        // 商品信息
                        Column.margin({ top: 8 });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.product.name);
                        Text.debugLine("entry/src/main/ets/pages/DetailsPage.ets(61:15)", "entry");
                        Text.fontSize(20);
                        Text.fontWeight(FontWeight.Bold);
                        Text.margin({ bottom: 8 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(`¥${this.product.price.toFixed(2)}`);
                        Text.debugLine("entry/src/main/ets/pages/DetailsPage.ets(66:15)", "entry");
                        Text.fontSize(24);
                        Text.fontColor('#ff6b35');
                        Text.fontWeight(FontWeight.Bold);
                        Text.margin({ bottom: 16 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        if (this.product.description) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Text.create('商品描述');
                                    Text.debugLine("entry/src/main/ets/pages/DetailsPage.ets(73:17)", "entry");
                                    Text.fontSize(16);
                                    Text.fontWeight(FontWeight.Medium);
                                    Text.margin({ bottom: 8 });
                                }, Text);
                                Text.pop();
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Text.create(this.product.description);
                                    Text.debugLine("entry/src/main/ets/pages/DetailsPage.ets(78:17)", "entry");
                                    Text.fontSize(14);
                                    Text.fontColor('#666');
                                    Text.lineHeight(20);
                                }, Text);
                                Text.pop();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    // 商品信息
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 数量选择
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/DetailsPage.ets(91:13)", "entry");
                        // 数量选择
                        Row.width('100%');
                        // 数量选择
                        Row.padding(16);
                        // 数量选择
                        Row.backgroundColor('#fff');
                        // 数量选择
                        Row.margin({ top: 8 });
                        // 数量选择
                        Row.justifyContent(FlexAlign.Start);
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('数量:');
                        Text.debugLine("entry/src/main/ets/pages/DetailsPage.ets(92:15)", "entry");
                        Text.fontSize(16);
                        Text.margin({ right: 16 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/DetailsPage.ets(96:15)", "entry");
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('-');
                        Button.debugLine("entry/src/main/ets/pages/DetailsPage.ets(97:17)", "entry");
                        Button.width(40);
                        Button.height(40);
                        Button.fontSize(18);
                        Button.enabled(this.quantity > 1);
                        Button.onClick(() => {
                            if (this.quantity > 1) {
                                this.quantity--;
                            }
                        });
                    }, Button);
                    Button.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.quantity.toString());
                        Text.debugLine("entry/src/main/ets/pages/DetailsPage.ets(108:17)", "entry");
                        Text.width(60);
                        Text.textAlign(TextAlign.Center);
                        Text.fontSize(16);
                        Text.margin({ left: 8, right: 8 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('+');
                        Button.debugLine("entry/src/main/ets/pages/DetailsPage.ets(114:17)", "entry");
                        Button.width(40);
                        Button.height(40);
                        Button.fontSize(18);
                        Button.onClick(() => {
                            this.quantity++;
                        });
                    }, Button);
                    Button.pop();
                    Row.pop();
                    // 数量选择
                    Row.pop();
                    Column.pop();
                    Scroll.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 底部操作栏
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/DetailsPage.ets(133:9)", "entry");
                        // 底部操作栏
                        Row.width('100%');
                        // 底部操作栏
                        Row.padding(16);
                        // 底部操作栏
                        Row.backgroundColor('#fff');
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('加入购物车');
                        Button.debugLine("entry/src/main/ets/pages/DetailsPage.ets(134:11)", "entry");
                        Button.layoutWeight(1);
                        Button.height(50);
                        Button.backgroundColor('#ffa500');
                        Button.margin({ right: 8 });
                        Button.onClick(() => {
                            if (this.product) {
                                this.cartViewModel.addToCart(ObservedObject.GetRawObject(this.product), this.quantity);
                                promptAction.showToast({
                                    message: `已将 ${this.product.name} 加入购物车`,
                                    duration: 2000
                                });
                            }
                        });
                    }, Button);
                    Button.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('立即购买');
                        Button.debugLine("entry/src/main/ets/pages/DetailsPage.ets(149:11)", "entry");
                        Button.layoutWeight(1);
                        Button.height(50);
                        Button.backgroundColor('#ff6b35');
                        Button.margin({ left: 8 });
                        Button.onClick(() => {
                            if (this.product) {
                                this.cartViewModel.addToCart(ObservedObject.GetRawObject(this.product), this.quantity);
                                router.pushUrl({
                                    url: 'pages/CartPage'
                                });
                            }
                        });
                    }, Button);
                    Button.pop();
                    // 底部操作栏
                    Row.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 商品不存在
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/DetailsPage.ets(168:9)", "entry");
                        // 商品不存在
                        Column.width('100%');
                        // 商品不存在
                        Column.height('60%');
                        // 商品不存在
                        Column.justifyContent(FlexAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('商品不存在');
                        Text.debugLine("entry/src/main/ets/pages/DetailsPage.ets(169:11)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#999');
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('返回');
                        Button.debugLine("entry/src/main/ets/pages/DetailsPage.ets(173:11)", "entry");
                        Button.margin({ top: 20 });
                        Button.onClick(() => {
                            router.back();
                        });
                    }, Button);
                    Button.pop();
                    // 商品不存在
                    Column.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "DetailsPage";
    }
}
registerNamedRoute(() => new DetailsPage(undefined, {}), "", { bundleName: "com.example.shi_yan_3", moduleName: "entry", pagePath: "pages/DetailsPage", pageFullPath: "entry/src/main/ets/pages/DetailsPage", integratedHsp: "false", moduleType: "followWithHap" });
