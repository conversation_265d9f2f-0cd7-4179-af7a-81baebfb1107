if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface DetailsPage_Params {
    product?: Product | undefined;
    quantity?: number;
    cartViewModel?: CartViewModel;
}
import type { Product } from '../model/Product';
import { DataSource } from "@normalized:N&&&entry/src/main/ets/model/DataSource&";
import { CartViewModel } from "@normalized:N&&&entry/src/main/ets/viewmodel/CartViewModel&";
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
class DetailsPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__product = new ObservedPropertyObjectPU(undefined, this, "product");
        this.__quantity = new ObservedPropertySimplePU(1, this, "quantity");
        this.cartViewModel = new CartViewModel();
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: DetailsPage_Params) {
        if (params.product !== undefined) {
            this.product = params.product;
        }
        if (params.quantity !== undefined) {
            this.quantity = params.quantity;
        }
        if (params.cartViewModel !== undefined) {
            this.cartViewModel = params.cartViewModel;
        }
    }
    updateStateVars(params: DetailsPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__product.purgeDependencyOnElmtId(rmElmtId);
        this.__quantity.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__product.aboutToBeDeleted();
        this.__quantity.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __product: ObservedPropertyObjectPU<Product | undefined>;
    get product() {
        return this.__product.get();
    }
    set product(newValue: Product | undefined) {
        this.__product.set(newValue);
    }
    private __quantity: ObservedPropertySimplePU<number>;
    get quantity() {
        return this.__quantity.get();
    }
    set quantity(newValue: number) {
        this.__quantity.set(newValue);
    }
    private cartViewModel: CartViewModel;
    aboutToAppear() {
        // 获取路由参数
        const params = router.getParams() as Record<string, Object>;
        if (params && params.productId) {
            this.product = DataSource.getProductById(params.productId as number);
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/DetailsPage.ets(26:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#f5f5f5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/DetailsPage.ets(28:7)", "entry");
            // 顶部导航栏
            Row.width('100%');
            // 顶部导航栏
            Row.height(56);
            // 顶部导航栏
            Row.padding({ left: 16, right: 16 });
            // 顶部导航栏
            Row.backgroundColor('#fff');
            // 顶部导航栏
            Row.shadow({ radius: 4, color: 'rgba(0, 0, 0, 0.1)', offsetY: 2 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild({ type: ButtonType.Circle });
            Button.debugLine("entry/src/main/ets/pages/DetailsPage.ets(29:9)", "entry");
            Button.width(40);
            Button.height(40);
            Button.backgroundColor('#F5F5F5');
            Button.onClick(() => {
                router.back();
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('←');
            Text.debugLine("entry/src/main/ets/pages/DetailsPage.ets(30:11)", "entry");
            Text.fontSize(20);
            Text.fontColor('#333');
        }, Text);
        Text.pop();
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/DetailsPage.ets(41:9)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('商品详情');
            Text.debugLine("entry/src/main/ets/pages/DetailsPage.ets(43:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Medium);
            Text.fontColor('#333');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/DetailsPage.ets(48:9)", "entry");
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 占位，保持居中
            Text.create('');
            Text.debugLine("entry/src/main/ets/pages/DetailsPage.ets(51:9)", "entry");
            // 占位，保持居中
            Text.width(40);
            // 占位，保持居中
            Text.height(40);
        }, Text);
        // 占位，保持居中
        Text.pop();
        // 顶部导航栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.product) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Scroll.create();
                        Scroll.debugLine("entry/src/main/ets/pages/DetailsPage.ets(62:9)", "entry");
                        Scroll.layoutWeight(1);
                    }, Scroll);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/DetailsPage.ets(63:11)", "entry");
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 商品图片
                        Stack.create({ alignContent: Alignment.Center });
                        Stack.debugLine("entry/src/main/ets/pages/DetailsPage.ets(65:13)", "entry");
                        // 商品图片
                        Stack.width('100%');
                        // 商品图片
                        Stack.height(300);
                        // 商品图片
                        Stack.backgroundColor('#F9F9F9');
                        // 商品图片
                        Stack.borderRadius(8);
                    }, Stack);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Image.create(this.product.image);
                        Image.debugLine("entry/src/main/ets/pages/DetailsPage.ets(66:15)", "entry");
                        Image.width('100%');
                        Image.height(300);
                        Image.objectFit(ImageFit.Contain);
                        Image.backgroundColor('#FFFFFF');
                        Image.onError(() => {
                            console.error(`Failed to load image: ${this.product?.image}`);
                        });
                    }, Image);
                    // 商品图片
                    Stack.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 商品信息
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/DetailsPage.ets(81:13)", "entry");
                        // 商品信息
                        Column.width('100%');
                        // 商品信息
                        Column.padding(16);
                        // 商品信息
                        Column.alignItems(HorizontalAlign.Start);
                        // 商品信息
                        Column.backgroundColor('#fff');
                        // 商品信息
                        Column.margin({ top: 8 });
                        // 商品信息
                        Column.borderRadius(8);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.product.name);
                        Text.debugLine("entry/src/main/ets/pages/DetailsPage.ets(82:15)", "entry");
                        Text.fontSize(20);
                        Text.fontWeight(FontWeight.Bold);
                        Text.margin({ bottom: 12 });
                        Text.textAlign(TextAlign.Start);
                        Text.width('100%');
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/DetailsPage.ets(89:15)", "entry");
                        Row.width('100%');
                        Row.margin({ bottom: 16 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(`¥${this.product.price.toFixed(2)}`);
                        Text.debugLine("entry/src/main/ets/pages/DetailsPage.ets(90:17)", "entry");
                        Text.fontSize(28);
                        Text.fontColor('#E41F19');
                        Text.fontWeight(FontWeight.Bold);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Blank.create();
                        Blank.debugLine("entry/src/main/ets/pages/DetailsPage.ets(95:17)", "entry");
                    }, Blank);
                    Blank.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('现货');
                        Text.debugLine("entry/src/main/ets/pages/DetailsPage.ets(97:17)", "entry");
                        Text.fontSize(12);
                        Text.fontColor('#4CAF50');
                        Text.backgroundColor('#E8F5E8');
                        Text.padding({ left: 8, right: 8, top: 4, bottom: 4 });
                        Text.borderRadius(4);
                    }, Text);
                    Text.pop();
                    Row.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 商品特色标签
                        Row.create({ space: 8 });
                        Row.debugLine("entry/src/main/ets/pages/DetailsPage.ets(108:15)", "entry");
                        // 商品特色标签
                        Row.width('100%');
                        // 商品特色标签
                        Row.margin({ bottom: 20 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('正品保证');
                        Text.debugLine("entry/src/main/ets/pages/DetailsPage.ets(109:17)", "entry");
                        Text.fontSize(10);
                        Text.fontColor('#E41F19');
                        Text.border({ width: 1, color: '#E41F19' });
                        Text.padding({ left: 6, right: 6, top: 2, bottom: 2 });
                        Text.borderRadius(2);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('7天退换');
                        Text.debugLine("entry/src/main/ets/pages/DetailsPage.ets(116:17)", "entry");
                        Text.fontSize(10);
                        Text.fontColor('#E41F19');
                        Text.border({ width: 1, color: '#E41F19' });
                        Text.padding({ left: 6, right: 6, top: 2, bottom: 2 });
                        Text.borderRadius(2);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('全国包邮');
                        Text.debugLine("entry/src/main/ets/pages/DetailsPage.ets(123:17)", "entry");
                        Text.fontSize(10);
                        Text.fontColor('#E41F19');
                        Text.border({ width: 1, color: '#E41F19' });
                        Text.padding({ left: 6, right: 6, top: 2, bottom: 2 });
                        Text.borderRadius(2);
                    }, Text);
                    Text.pop();
                    // 商品特色标签
                    Row.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        if (this.product.description) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Text.create('商品详情');
                                    Text.debugLine("entry/src/main/ets/pages/DetailsPage.ets(134:17)", "entry");
                                    Text.fontSize(16);
                                    Text.fontWeight(FontWeight.Medium);
                                    Text.margin({ bottom: 8 });
                                    Text.textAlign(TextAlign.Start);
                                    Text.width('100%');
                                }, Text);
                                Text.pop();
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Text.create(this.product.description);
                                    Text.debugLine("entry/src/main/ets/pages/DetailsPage.ets(141:17)", "entry");
                                    Text.fontSize(14);
                                    Text.fontColor('#666');
                                    Text.lineHeight(22);
                                    Text.textAlign(TextAlign.Start);
                                    Text.width('100%');
                                }, Text);
                                Text.pop();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    // 商品信息
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 数量选择和规格
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/DetailsPage.ets(157:13)", "entry");
                        // 数量选择和规格
                        Column.width('100%');
                        // 数量选择和规格
                        Column.padding(16);
                        // 数量选择和规格
                        Column.backgroundColor('#fff');
                        // 数量选择和规格
                        Column.margin({ top: 8 });
                        // 数量选择和规格
                        Column.alignItems(HorizontalAlign.Start);
                        // 数量选择和规格
                        Column.borderRadius(8);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 数量选择
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/DetailsPage.ets(159:15)", "entry");
                        // 数量选择
                        Row.width('100%');
                        // 数量选择
                        Row.margin({ bottom: 16 });
                        // 数量选择
                        Row.justifyContent(FlexAlign.Start);
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('数量:');
                        Text.debugLine("entry/src/main/ets/pages/DetailsPage.ets(160:17)", "entry");
                        Text.fontSize(16);
                        Text.fontWeight(FontWeight.Medium);
                        Text.margin({ right: 16 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/DetailsPage.ets(165:17)", "entry");
                        Row.alignItems(VerticalAlign.Center);
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('-');
                        Button.debugLine("entry/src/main/ets/pages/DetailsPage.ets(166:19)", "entry");
                        Button.width(36);
                        Button.height(36);
                        Button.fontSize(18);
                        Button.backgroundColor(this.quantity > 1 ? '#E41F19' : '#CCCCCC');
                        Button.fontColor('#FFFFFF');
                        Button.enabled(this.quantity > 1);
                        Button.onClick(() => {
                            if (this.quantity > 1) {
                                this.quantity--;
                            }
                        });
                    }, Button);
                    Button.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.quantity.toString());
                        Text.debugLine("entry/src/main/ets/pages/DetailsPage.ets(179:19)", "entry");
                        Text.width(50);
                        Text.textAlign(TextAlign.Center);
                        Text.fontSize(16);
                        Text.margin({ left: 8, right: 8 });
                        Text.border({ width: 1, color: '#E0E0E0' });
                        Text.height(36);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('+');
                        Button.debugLine("entry/src/main/ets/pages/DetailsPage.ets(187:19)", "entry");
                        Button.width(36);
                        Button.height(36);
                        Button.fontSize(18);
                        Button.backgroundColor('#E41F19');
                        Button.fontColor('#FFFFFF');
                        Button.onClick(() => {
                            this.quantity++;
                        });
                    }, Button);
                    Button.pop();
                    Row.pop();
                    // 数量选择
                    Row.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 配送信息
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/DetailsPage.ets(204:15)", "entry");
                        // 配送信息
                        Row.width('100%');
                        // 配送信息
                        Row.justifyContent(FlexAlign.Start);
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('配送:');
                        Text.debugLine("entry/src/main/ets/pages/DetailsPage.ets(205:17)", "entry");
                        Text.fontSize(16);
                        Text.fontWeight(FontWeight.Medium);
                        Text.margin({ right: 16 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('全国包邮 · 预计1-3天到达');
                        Text.debugLine("entry/src/main/ets/pages/DetailsPage.ets(210:17)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#666');
                    }, Text);
                    Text.pop();
                    // 配送信息
                    Row.pop();
                    // 数量选择和规格
                    Column.pop();
                    Column.pop();
                    Scroll.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 底部操作栏
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/DetailsPage.ets(228:9)", "entry");
                        // 底部操作栏
                        Row.width('100%');
                        // 底部操作栏
                        Row.padding(16);
                        // 底部操作栏
                        Row.backgroundColor('#fff');
                        // 底部操作栏
                        Row.shadow({ radius: 8, color: 'rgba(0, 0, 0, 0.1)', offsetY: -2 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('加入购物车');
                        Button.debugLine("entry/src/main/ets/pages/DetailsPage.ets(229:11)", "entry");
                        Button.layoutWeight(1);
                        Button.height(50);
                        Button.fontSize(16);
                        Button.fontWeight(FontWeight.Medium);
                        Button.backgroundColor('#FFA500');
                        Button.fontColor('#FFFFFF');
                        Button.margin({ right: 8 });
                        Button.borderRadius(25);
                        Button.onClick(() => {
                            if (this.product) {
                                this.cartViewModel.addToCart(ObservedObject.GetRawObject(this.product), this.quantity);
                                promptAction.showToast({
                                    message: `已将 ${this.product.name} 加入购物车`,
                                    duration: 2000
                                });
                            }
                        });
                    }, Button);
                    Button.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('立即购买');
                        Button.debugLine("entry/src/main/ets/pages/DetailsPage.ets(248:11)", "entry");
                        Button.layoutWeight(1);
                        Button.height(50);
                        Button.fontSize(16);
                        Button.fontWeight(FontWeight.Medium);
                        Button.backgroundColor('#E41F19');
                        Button.fontColor('#FFFFFF');
                        Button.margin({ left: 8 });
                        Button.borderRadius(25);
                        Button.onClick(() => {
                            if (this.product) {
                                this.cartViewModel.addToCart(ObservedObject.GetRawObject(this.product), this.quantity);
                                router.pushUrl({
                                    url: 'pages/CartPage'
                                });
                            }
                        });
                    }, Button);
                    Button.pop();
                    // 底部操作栏
                    Row.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 商品不存在
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/DetailsPage.ets(272:9)", "entry");
                        // 商品不存在
                        Column.width('100%');
                        // 商品不存在
                        Column.height('60%');
                        // 商品不存在
                        Column.justifyContent(FlexAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('商品不存在');
                        Text.debugLine("entry/src/main/ets/pages/DetailsPage.ets(273:11)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#999');
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('返回');
                        Button.debugLine("entry/src/main/ets/pages/DetailsPage.ets(277:11)", "entry");
                        Button.margin({ top: 20 });
                        Button.onClick(() => {
                            router.back();
                        });
                    }, Button);
                    Button.pop();
                    // 商品不存在
                    Column.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "DetailsPage";
    }
}
registerNamedRoute(() => new DetailsPage(undefined, {}), "", { bundleName: "com.example.shi_yan_3", moduleName: "entry", pagePath: "pages/DetailsPage", pageFullPath: "entry/src/main/ets/pages/DetailsPage", integratedHsp: "false", moduleType: "followWithHap" });
