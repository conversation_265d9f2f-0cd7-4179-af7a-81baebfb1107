import { CartViewModel } from '../viewmodel/CartViewModel';
import { CartItem } from '../model/CartItem';
import { Constants } from '../common/Constants';
import router from '@ohos.router';

/**
 * 购物车页面
 */
@Entry
@Component
struct CartPage {
  @StorageLink(Constants.SHOPPING_CART) cartItems: CartItem[] = [];
  private cartViewModel: CartViewModel = new CartViewModel();

  build() {
    Column() {
      // 标题栏
      Row() {
        Button('返回')
          .onClick(() => {
            router.back();
          })
          .margin({ left: 16 })

        Text('购物车')
          .fontSize(20)
          .fontWeight(FontWeight.Bold)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Text(`共${this.cartViewModel.getTotalQuantity()}件商品`)
          .fontSize(14)
          .fontColor('#666')
          .margin({ right: 16 })
      }
      .width('100%')
      .height(56)
      .backgroundColor('#f8f8f8')

      if (this.cartItems.length === 0) {
        // 空购物车状态
        Column() {
          Text('购物车是空的')
            .fontSize(16)
            .fontColor('#999')
          
          Button('去购物')
            .margin({ top: 20 })
            .onClick(() => {
              router.back();
            })
        }
        .width('100%')
        .height('60%')
        .justifyContent(FlexAlign.Center)
      } else {
        // 购物车列表
        List() {
          ForEach(this.cartItems, (item: CartItem) => {
            ListItem() {
              Row() {
                // 商品图片占位
                Text('图片')
                  .width(60)
                  .height(60)
                  .backgroundColor('#f0f0f0')
                  .textAlign(TextAlign.Center)
                  .margin({ right: 12 })

                Column() {
                  Text(item.product.name)
                    .fontSize(16)
                    .fontWeight(FontWeight.Medium)
                  
                  Text(`¥${item.product.price.toFixed(2)}`)
                    .fontSize(14)
                    .fontColor('#ff6b35')
                    .margin({ top: 4 })
                }
                .alignItems(HorizontalAlign.Start)
                .layoutWeight(1)

                // 数量控制和删除
                Column() {
                  // 删除按钮
                  Button('删除')
                    .width(50)
                    .height(24)
                    .fontSize(10)
                    .backgroundColor('#ff4444')
                    .margin({ bottom: 8 })
                    .onClick(() => {
                      this.cartViewModel.removeFromCart(item.product.id);
                    })

                  // 数量控制
                  Row() {
                    Button('-')
                      .width(28)
                      .height(28)
                      .fontSize(14)
                      .enabled(item.quantity > 1)
                      .onClick(() => {
                        this.cartViewModel.updateQuantity(item.product.id, item.quantity - 1);
                      })

                    Text(item.quantity.toString())
                      .width(30)
                      .textAlign(TextAlign.Center)
                      .fontSize(14)
                      .margin({ left: 4, right: 4 })

                    Button('+')
                      .width(28)
                      .height(28)
                      .fontSize(14)
                      .onClick(() => {
                        this.cartViewModel.updateQuantity(item.product.id, item.quantity + 1);
                      })
                  }
                  .justifyContent(FlexAlign.Center)
                }
                .alignItems(HorizontalAlign.End)
              }
              .width('100%')
              .padding(16)
            }
          })
        }
        .layoutWeight(1)

        // 底部结算栏
        Row() {
          Column() {
            Text(`总计: ¥${this.cartViewModel.getTotalPrice().toFixed(2)}`)
              .fontSize(18)
              .fontWeight(FontWeight.Bold)
              .fontColor('#ff6b35')
          }
          .layoutWeight(1)
          .alignItems(HorizontalAlign.Start)

          Button('结算')
            .width(100)
            .height(40)
            .backgroundColor('#ff6b35')
            .onClick(() => {
              // 清空购物车
              this.cartViewModel.clearCart();
              // 跳转到结算成功页面
              router.pushUrl({
                url: 'pages/CheckoutSuccessPage'
              });
            })
        }
        .width('100%')
        .height(80)
        .padding({ left: 16, right: 16 })
        .backgroundColor('#fff')
        .border({ width: { top: 1 }, color: '#e0e0e0' })
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#f5f5f5')
  }
}
