import { Product } from '../model/Product';
import { DataSource } from '../model/DataSource';
import { CartViewModel } from '../viewmodel/CartViewModel';
import router from '@ohos.router';

/**
 * 商品详情页
 */
@Entry
@Component
struct DetailsPage {
  @State product: Product | undefined = undefined;
  @State quantity: number = 1;
  private cartViewModel: CartViewModel = new CartViewModel();

  aboutToAppear() {
    // 获取路由参数
    const params = router.getParams() as { productId: number };
    if (params && params.productId) {
      this.product = DataSource.getProductById(params.productId);
    }
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Button('返回')
          .onClick(() => {
            router.back();
          })
        
        Blank()
        
        Text('商品详情')
          .fontSize(18)
          .fontWeight(FontWeight.Medium)
        
        Blank()
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor('#fff')

      if (this.product) {
        ScrollView() {
          Column() {
            // 商品图片
            Text('商品图片')
              .width('100%')
              .height(300)
              .backgroundColor('#f0f0f0')
              .textAlign(TextAlign.Center)
              .fontSize(16)
              .fontColor('#999')

            // 商品信息
            Column() {
              Text(this.product.name)
                .fontSize(20)
                .fontWeight(FontWeight.Bold)
                .margin({ bottom: 8 })

              Text(`¥${this.product.price.toFixed(2)}`)
                .fontSize(24)
                .fontColor('#ff6b35')
                .fontWeight(FontWeight.Bold)
                .margin({ bottom: 16 })

              if (this.product.description) {
                Text('商品描述')
                  .fontSize(16)
                  .fontWeight(FontWeight.Medium)
                  .margin({ bottom: 8 })

                Text(this.product.description)
                  .fontSize(14)
                  .fontColor('#666')
                  .lineHeight(20)
              }
            }
            .width('100%')
            .padding(16)
            .alignItems(HorizontalAlign.Start)
            .backgroundColor('#fff')
            .margin({ top: 8 })

            // 数量选择
            Row() {
              Text('数量:')
                .fontSize(16)
                .margin({ right: 16 })

              Row() {
                Button('-')
                  .width(40)
                  .height(40)
                  .fontSize(18)
                  .enabled(this.quantity > 1)
                  .onClick(() => {
                    if (this.quantity > 1) {
                      this.quantity--;
                    }
                  })

                Text(this.quantity.toString())
                  .width(60)
                  .textAlign(TextAlign.Center)
                  .fontSize(16)
                  .margin({ left: 8, right: 8 })

                Button('+')
                  .width(40)
                  .height(40)
                  .fontSize(18)
                  .onClick(() => {
                    this.quantity++;
                  })
              }
            }
            .width('100%')
            .padding(16)
            .backgroundColor('#fff')
            .margin({ top: 8 })
            .justifyContent(FlexAlign.Start)
          }
        }
        .layoutWeight(1)

        // 底部操作栏
        Row() {
          Button('加入购物车')
            .layoutWeight(1)
            .height(50)
            .backgroundColor('#ffa500')
            .margin({ right: 8 })
            .onClick(() => {
              if (this.product) {
                this.cartViewModel.addToCart(this.product, this.quantity);
                // 显示提示信息
                // TODO: 添加 Toast 提示
                console.log('已加入购物车');
              }
            })

          Button('立即购买')
            .layoutWeight(1)
            .height(50)
            .backgroundColor('#ff6b35')
            .margin({ left: 8 })
            .onClick(() => {
              if (this.product) {
                this.cartViewModel.addToCart(this.product, this.quantity);
                router.pushUrl({
                  url: 'pages/CartPage'
                });
              }
            })
        }
        .width('100%')
        .padding(16)
        .backgroundColor('#fff')
      } else {
        // 商品不存在
        Column() {
          Text('商品不存在')
            .fontSize(16)
            .fontColor('#999')
          
          Button('返回')
            .margin({ top: 20 })
            .onClick(() => {
              router.back();
            })
        }
        .width('100%')
        .height('60%')
        .justifyContent(FlexAlign.Center)
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#f5f5f5')
  }
}
