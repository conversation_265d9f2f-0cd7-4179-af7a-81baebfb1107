// 202306110141 杨富涛

import { router } from '@kit.ArkUI';

@Entry
@Component
struct LoginPage {
  @Builder
  LoginPageContent() {
    Column() {
      Image('https://image2.135editor.com/cache/remote/aHR0cHM6Ly9tbWJpei5xbG9nby5jbi9tbWJpel9qcGcvQUlKZ00zYmt3OG03SGhtb3IxRzdYMzZEVkFjMThpYVhHaWFUaWJPaENVSGpsd3JYVmdMU1pRSWFYTEloZmhZYXByVXJnTVAxV2pFb1I2aWNmRXRwd25jb0N3LzY0MC5qcGVn')
  .width(100)
  .height(100)
  .borderRadius(20)
  .margin({ top: 100, bottom: 50 })

      TextInput({ placeholder: '用户名' })
        .width('80%')
        .height(50)
        .margin(10)
        .backgroundColor(Color.White)

      TextInput({ placeholder: '密码' })
        .type(InputType.Password)
        .width('80%')
        .height(50)
        .margin(10)
        .backgroundColor(Color.White)

      Button('登录')
        .width('60%')
        .height(50)
        .margin(30)
        .onClick(() => {
          router.replace({ url: 'pages/Index' });
        })

      Row() {
        Text('还没有账号？').fontColor(Color.Black)
        Text('去注册')
          .decoration({ type: TextDecorationType.Underline, color: Color.Blue })
          .fontColor(Color.Blue)
          .onClick(() => {
            router.replace({ url: 'pages/RegisterPage' });
          })
      }
      .margin({ top: 10 })
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Start)
    .alignItems(HorizontalAlign.Center)
    .backgroundColor('#F1F3F5')
  }

  build() {
    Column() {
      this.LoginPageContent();
    }
  }
}
