import { Constants } from '../common/Constants';
import { CartItem } from '../model/CartItem';

/**
 * 底部导航栏容器组件 - 应用的主导航容器
 */
@Entry
@Component
struct BottomTabs {
  @StorageLink(Constants.SHOPPING_CART) cartItems: CartItem[] = [];
  @State currentIndex: number = 0;

  /**
   * 计算购物车中商品总数量
   */
  private getCartTotalQuantity(): number {
    return this.cartItems.reduce((total, item) => total + item.quantity, 0);
  }

  build() {
    Tabs({ barPosition: BarPosition.End, index: this.currentIndex }) {
      // 首页标签
      TabContent() {
        // 这里应该是 Index 页面的内容
        // 由于架构要求，我们暂时放置占位内容
        Column() {
          Text('首页内容')
            .fontSize(20)
            .margin(20)
          
          Text('请在 Index.ets 中实现具体的首页内容')
            .fontSize(14)
            .fontColor('#666')
        }
        .width('100%')
        .height('100%')
        .justifyContent(FlexAlign.Center)
      }
      .tabBar(this.TabBuilder('首页', 0, $r('sys.symbol.house')))

      // 购物车标签
      TabContent() {
        // 这里应该是 CartPage 页面的内容
        // 由于架构要求，我们暂时放置占位内容
        Column() {
          Text('购物车内容')
            .fontSize(20)
            .margin(20)
          
          Text('请在 CartPage.ets 中实现具体的购物车内容')
            .fontSize(14)
            .fontColor('#666')
        }
        .width('100%')
        .height('100%')
        .justifyContent(FlexAlign.Center)
      }
      .tabBar(this.TabBuilder('购物车', 1, $r('sys.symbol.cart'), this.getCartTotalQuantity()))
    }
    .onChange((index: number) => {
      this.currentIndex = index;
    })
  }

  /**
   * 构建标签栏项
   */
  @Builder
  TabBuilder(title: string, targetIndex: number, icon: Resource, badgeCount?: number) {
    Column() {
      Stack() {
        Image(icon)
          .width(24)
          .height(24)
          .fillColor(this.currentIndex === targetIndex ? '#ff6b35' : '#999')

        // 购物车徽章
        if (badgeCount && badgeCount > 0) {
          Text(badgeCount > 99 ? '99+' : badgeCount.toString())
            .fontSize(10)
            .fontColor('#fff')
            .backgroundColor('#ff4444')
            .borderRadius(8)
            .padding({ left: 4, right: 4, top: 1, bottom: 1 })
            .position({ x: 12, y: -4 })
        }
      }
      .margin({ bottom: 4 })

      Text(title)
        .fontSize(12)
        .fontColor(this.currentIndex === targetIndex ? '#ff6b35' : '#999')
    }
    .width('100%')
    .height(56)
    .justifyContent(FlexAlign.Center)
  }
}
