import { CartItem } from '../model/CartItem';
import { CartViewModel } from '../viewmodel/CartViewModel';

/**
 * 购物车列表项组件 - 可复用的购物车商品展示组件
 */
@Component
export struct CartItemView {
  @Prop cartItem: CartItem;
  private cartViewModel: CartViewModel = new CartViewModel();

  build() {
    Row() {
      // 商品图片
      Text('图片')
        .width(60)
        .height(60)
        .backgroundColor('#f0f0f0')
        .textAlign(TextAlign.Center)
        .fontSize(12)
        .fontColor('#999')
        .borderRadius(4)
        .margin({ right: 12 })

      // 商品信息
      Column() {
        Text(this.cartItem.product.name)
          .fontSize(16)
          .fontWeight(FontWeight.Medium)
          .maxLines(2)
          .textOverflow({ overflow: TextOverflow.Ellipsis })
          .margin({ bottom: 4 })

        Text(`¥${this.cartItem.product.price.toFixed(2)}`)
          .fontSize(14)
          .fontColor('#ff6b35')
          .fontWeight(FontWeight.Bold)

        if (this.cartItem.product.description) {
          Text(this.cartItem.product.description)
            .fontSize(12)
            .fontColor('#999')
            .maxLines(1)
            .textOverflow({ overflow: TextOverflow.Ellipsis })
            .margin({ top: 4 })
        }
      }
      .alignItems(HorizontalAlign.Start)
      .layoutWeight(1)

      // 数量控制和删除
      Column() {
        // 删除按钮
        Button('删除')
          .width(50)
          .height(24)
          .fontSize(10)
          .backgroundColor('#ff4444')
          .margin({ bottom: 8 })
          .onClick(() => {
            this.cartViewModel.removeFromCart(this.cartItem.product.id);
          })

        // 数量控制
        Row() {
          Button('-')
            .width(28)
            .height(28)
            .fontSize(14)
            .enabled(this.cartItem.quantity > 1)
            .onClick(() => {
              this.cartViewModel.updateQuantity(this.cartItem.product.id, this.cartItem.quantity - 1);
            })

          Text(this.cartItem.quantity.toString())
            .width(30)
            .textAlign(TextAlign.Center)
            .fontSize(14)
            .margin({ left: 4, right: 4 })

          Button('+')
            .width(28)
            .height(28)
            .fontSize(14)
            .onClick(() => {
              this.cartViewModel.updateQuantity(this.cartItem.product.id, this.cartItem.quantity + 1);
            })
        }
        .justifyContent(FlexAlign.Center)
      }
      .alignItems(HorizontalAlign.End)
    }
    .width('100%')
    .padding(12)
    .backgroundColor('#fff')
    .borderRadius(8)
    .margin({ bottom: 8 })
    .shadow({ radius: 2, color: '#00000008' })
  }
}
