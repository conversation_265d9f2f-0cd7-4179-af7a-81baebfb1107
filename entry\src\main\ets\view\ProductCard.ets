import { Product } from '../model/Product';
import { CartViewModel } from '../viewmodel/CartViewModel';
import router from '@ohos.router';

/**
 * 商品卡片组件 - 可复用的商品展示组件
 */
@Component
export struct ProductCard {
  @Prop product: Product;
  private cartViewModel: CartViewModel = new CartViewModel();

  build() {
    Column() {
      // 商品图片
      Text('商品图片')
        .width('100%')
        .height(120)
        .backgroundColor('#f0f0f0')
        .textAlign(TextAlign.Center)
        .fontSize(12)
        .fontColor('#999')
        .onClick(() => {
          // 跳转到商品详情页
          router.pushUrl({
            url: 'pages/DetailsPage',
            params: { productId: this.product.id }
          });
        })

      // 商品信息
      Column() {
        Text(this.product.name)
          .fontSize(14)
          .fontWeight(FontWeight.Medium)
          .maxLines(2)
          .textOverflow({ overflow: TextOverflow.Ellipsis })
          .margin({ bottom: 4 })

        Text(`¥${this.product.price.toFixed(2)}`)
          .fontSize(16)
          .fontColor('#ff6b35')
          .fontWeight(FontWeight.Bold)
          .margin({ bottom: 8 })

        // 加入购物车按钮
        Button('加入购物车')
          .width('100%')
          .height(32)
          .fontSize(12)
          .backgroundColor('#ff6b35')
          .onClick(() => {
            this.cartViewModel.addToCart(this.product, 1);
            // TODO: 添加成功提示
            console.log(`已将 ${this.product.name} 加入购物车`);
          })
      }
      .width('100%')
      .padding(8)
      .alignItems(HorizontalAlign.Start)
    }
    .width('100%')
    .backgroundColor('#fff')
    .borderRadius(8)
    .shadow({ radius: 4, color: '#00000010' })
    .onClick(() => {
      // 点击卡片跳转到详情页
      router.pushUrl({
        url: 'pages/DetailsPage',
        params: { productId: this.product.id }
      });
    })
  }
}
